{"name": "MatchRegistrationData", "schema": {"required": ["matchId", "playerId"], "type": "object", "properties": {"matchId": {"type": "number"}, "playerId": {"type": "number"}, "styleDivision": {"type": "string"}, "ageDivision": {"type": "string"}, "genderDivision": {"type": "string"}, "registrationDetails": {}, "equipmentId": {"type": "number"}, "isPaid": {"type": "boolean"}, "isConfirmed": {"type": "boolean"}}, "additionalProperties": false}}