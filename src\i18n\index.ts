import { createI18n } from 'vue-i18n'
import en from './locales/en.json'
import pl from './locales/pl.json'

export type MessageLanguages = keyof typeof en
export type MessageSchema = typeof en

// Get saved locale from localStorage or default to 'en'
const savedLocale = localStorage.getItem('preferred-language') || 'en'

const i18n = createI18n<[MessageSchema], MessageLanguages>({
  legacy: false, // Enable Composition API mode
  locale: savedLocale, // Use saved locale
  fallbackLocale: 'en',
  messages: {
    en,
    pl,
  },
})

export default i18n
