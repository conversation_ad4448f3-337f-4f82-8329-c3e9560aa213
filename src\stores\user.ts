import { defineStore } from 'pinia'
import { computed, ref, watch } from 'vue'

import { useStorage } from '@vueuse/core'

import type { Equipment, Organizer, Player, User, UserPatch, UserProfile } from '../api/feathers-client'
import { api } from '../api/feathers-client'
import { useAuthStore } from './auth'

/**
 * User store for storing user profile and related data from the user-me service.
 * All related api persistence should be done by dedicated services (player, equipment)
 *
 * This store automatically fetches the user profile when the user is authenticated
 * and clears it when the user logs out.
 *
 * Usage:
 * ```ts
 * const userStore = useUserStore()
 *
 * // Access user profile
 * console.log(userStore.userProfile)
 *
 * // Update user profile
 * await userStore.updateUserProfile({ avatar: 'new-avatar-url' })
 *
 * // Manually fetch profile
 * await userStore.fetchUserProfile()
 * ```
 */

export const useUserStore = defineStore('user', () => {
  // Use the typed service from the api client
  const userMeService = api.userMe
  const userService = api.users

  const userProfile = ref<UserProfile | null>(null)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  // Persisted state for active player and organizer
  const activePlayerId = useStorage<number | null>('activePlayerId', null)
  const activeOrganizerId = useStorage<number | null>('activeOrganizerId', null)
  const playerEquipmentSelection = useStorage<Record<number, number>>('playerEquipmentSelection', {})

  // Computed properties
  const hasProfile = computed(() => !!userProfile.value)
  const fullName = computed(() => {
    if (!userProfile.value) return ''

    // Try to get name from the first player profile
    const firstPlayer = userProfile.value.players?.[0]
    if (firstPlayer?.firstname || firstPlayer?.lastname) {
      const firstName = firstPlayer.firstname || ''
      const lastName = firstPlayer.lastname || ''
      return `${firstName} ${lastName}`.trim()
    }

    // Fallback to email
    return userProfile.value.email
  })

  const primaryPlayer = computed(() => {
    return userProfile.value?.players?.[0] || null
  })

  const activePlayer = computed(() => {
    if (!userProfile.value?.players || !activePlayerId.value) return primaryPlayer.value
    const player = userProfile.value.players.find((p: Player) => p.id === activePlayerId.value) || primaryPlayer.value

    if (player && playerEquipmentSelection.value[player.id]) {
      return {
        ...player,
        activeEquipmentId: playerEquipmentSelection.value[player.id],
      }
    }
    return player
  })

  const activeOrganizer = computed(() => {
    if (!userProfile.value?.organizers || !activeOrganizerId.value) return userProfile.value?.organizers?.[0] || null
    return userProfile.value.organizers.find((o: Organizer) => o.id === activeOrganizerId.value)
  })

  const playerLocation = computed(() => {
    const player = activePlayer.value
    if (!player) return null

    const parts = [player.city, player.country].filter(Boolean)
    return parts.length > 0 ? parts.join(', ') : null
  })


  // Actions
  // Actions
  function setActivePlayer(playerId: number) {
    const playerExists = userProfile.value?.players?.some((p: Player) => p.id === playerId)
    if (playerExists) {
      activePlayerId.value = playerId
    }
  }

  function setActiveOrganizer(organizerId: number) {
    const organizerExists = userProfile.value?.organizers?.some((o: Organizer) => o.id === organizerId)
    if (organizerExists) {
      activeOrganizerId.value = organizerId
    }
  }

  async function setActiveEquipment(equipment: Equipment) {
    if (!activePlayer.value?.id) {
      error.value = new Error('No active player selected')
      return
    }
    playerEquipmentSelection.value[activePlayer.value.id] = equipment.id
  }

  async function fetchUserProfile() {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      // user-me is a singleton service, find returns the single record.
      const profile = await userMeService.find({} as any)
      userProfile.value = profile

      // Set default active player/organizer if not set
      if (!activePlayerId.value && profile.players?.[0]) {
        activePlayerId.value = profile.players[0].id
      }
      if (!activeOrganizerId.value && profile.organizers?.[0]) {
        activeOrganizerId.value = profile.organizers[0].id
      }
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to fetch user profile')
      }
      userProfile.value = null
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateUserProfile(data: Partial<User>) {
    const authStore = useAuthStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    isLoading.value = true
    error.value = null

    try {
      if (!userProfile.value?.id) throw new Error('User ID not found')
      const updatedProfile = await userService.patch(userProfile.value.id, data, {})
      // Merge the updated data with the current profile
      if (userProfile.value) {
        userProfile.value = { ...userProfile.value, ...updatedProfile }
      }
      return updatedProfile
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to update user profile')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  function clearUserProfile() {
    userProfile.value = null
    activePlayerId.value = null
    activeOrganizerId.value = null
    // Do not clear playerEquipmentSelection, as it should persist for the user
    error.value = null
  }

  // Watch for authentication changes and auto-fetch profile
  const authStore = useAuthStore()

  // Auto-fetch profile when user becomes authenticated
  watch(
    () => authStore.isAuthenticated,
    (isAuthenticated) => {
      if (isAuthenticated && !userProfile.value) {
        fetchUserProfile()
      } else if (!isAuthenticated) {
        clearUserProfile()
      }
    },
    { immediate: true }
  )

  return {
    userProfile,
    isLoading,
    error,
    hasProfile,
    fullName,
    primaryPlayer,
    activePlayer,
    activeOrganizer,
    playerLocation,
    fetchUserProfile,
    updateUserProfile,
    clearUserProfile,
    setActivePlayer,
    setActiveOrganizer,
    setActiveEquipment,
  }
})
