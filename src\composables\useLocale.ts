import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLocale() {
  const { locale, t } = useI18n()

  // Watch for locale changes and persist to localStorage
  watch(locale, (newLocale) => {
    localStorage.setItem('preferred-language', newLocale)
    // Update document language attribute
    document.documentElement.lang = newLocale
  }, { immediate: true })

  const setLocale = (newLocale: string) => {
    locale.value = newLocale
  }

  const getAvailableLocales = computed(() => [
    { code: 'en', label: t('languages.english'), flag: '/flags/gb.svg' },
    { code: 'pl', label: t('languages.polski'), flag: '/flags/pl.svg' },
  ])

  return {
    locale,
    setLocale,
    getAvailableLocales,
    t
  }
}
