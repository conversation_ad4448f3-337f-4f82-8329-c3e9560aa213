<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useMatchesStore } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import SimpleMatchesList from '@/components/matches/SimpleMatchesList.vue'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
} from '@/components/ui/sidebar'
import { Calendar, Users, Trophy, AlertCircle } from 'lucide-vue-next'

const matchesStore = useMatchesStore()
const userStore = useUserStore()

const {
  matches,
  matchRegistrations,
  currentMatch,
  isLoading: isLoadingMatches,
  isLoadingRegistrations,
  error
} = storeToRefs(matchesStore)

const { primaryPlayer } = storeToRefs(userStore)

const isLoading = computed(() => isLoadingMatches.value || isLoadingRegistrations.value)

// Get matches where the user is registered
const myMatches = computed(() => {
  if (!primaryPlayer.value || !matchRegistrations.value.length) {
    return []
  }

  const playerRegistrations = matchRegistrations.value.filter(
    reg => reg.playerId === primaryPlayer.value!.id
  )

  // Get the matches for these registrations
  const myMatchIds = playerRegistrations.map(reg => reg.matchId)
  return matches.value.filter(match => myMatchIds.includes(match.id))
})

// Group matches by status
const upcomingMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return myMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate >= today
  })
})

const pastMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return myMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate < today
  })
})

onMounted(async () => {
  try {
    // Fetch user's match registrations
    if (primaryPlayer.value) {
      await matchesStore.findMatchRegistrations({
        query: {
          playerId: primaryPlayer.value.id,
          $limit: 100
        }
      })
    }

    // Fetch matches (if not already loaded)
    if (matches.value.length === 0) {
      await matchesStore.findMatches({
        query: {
          $limit: 100,
          isActive: true
        }
      })
    }
  } catch (err) {
    console.error('Error loading my matches:', err)
  }
})
</script>

<template>
  <SidebarProvider>
    <SidebarInset>
      <div class="min-h-screen bg-background">
        <div class="container mx-auto px-4 py-6">
          <!-- Header -->
          <div class="mb-6">
            <h1 class="text-3xl font-bold mb-2">My Matches</h1>
            <p class="text-muted-foreground">Competitions you've signed up for</p>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="flex items-center justify-center py-12">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p class="text-muted-foreground">Loading your matches...</p>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="flex items-center justify-center py-12">
            <Card class="w-full max-w-md">
              <CardContent class="pt-6">
                <div class="text-center">
                  <AlertCircle class="h-8 w-8 text-red-500 mx-auto mb-4" />
                  <p class="text-red-500 mb-2">Error loading matches</p>
                  <p class="text-muted-foreground text-sm">{{ error.message }}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- No Matches State -->
          <div v-else-if="myMatches.length === 0" class="flex items-center justify-center py-12">
            <Card class="w-full max-w-md">
              <CardContent class="pt-6">
                <div class="text-center">
                  <Trophy class="h-8 w-8 text-muted-foreground mx-auto mb-4" />
                  <h3 class="text-lg font-semibold mb-2">No matches yet</h3>
                  <p class="text-muted-foreground mb-4">You haven't signed up for any competitions yet.</p>
                  <Button>
                    <router-link to="/matches/search">
                      Browse Matches
                    </router-link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Matches Content -->
          <div v-else class="space-y-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Calendar class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ upcomingMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">Upcoming</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Trophy class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ pastMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">Completed</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Users class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ myMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">Total</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Upcoming Matches -->
            <div v-if="upcomingMatches.length > 0">
              <div class="flex items-center gap-4 mb-4">
                <h2 class="text-2xl font-semibold">Upcoming Matches</h2>
                <Badge variant="secondary">{{ upcomingMatches.length }}</Badge>
              </div>
              <SimpleMatchesList :items="upcomingMatches" />
            </div>

            <Separator v-if="upcomingMatches.length > 0 && pastMatches.length > 0" />

            <!-- Past Matches -->
            <div v-if="pastMatches.length > 0">
              <div class="flex items-center gap-4 mb-4">
                <h2 class="text-2xl font-semibold">Past Matches</h2>
                <Badge variant="outline">{{ pastMatches.length }}</Badge>
              </div>
              <SimpleMatchesList :items="pastMatches" />
            </div>
          </div>
        </div>
      </div>
    </SidebarInset>

    <!-- Match Details Sidebar -->
    <Sidebar
      class="sticky top-0 h-svh border-l"
      collapsible="offcanvas"
    >
      <SidebarContent>
        <MatchDetailsWidget :match="currentMatch" />
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
