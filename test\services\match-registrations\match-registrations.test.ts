// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { afterEach, beforeEach, describe, it } from 'mocha'

import { BadRequest } from '@feathersjs/errors'

import { app } from '../../../src/app'
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('match-registrations service', () => {
  const service = app.service('match-registrations')
  const matchesService = app.service('matches')
  const playersService = app.service('players')
  const usersService = app.service('users')
  const authService = app.service('authentication')

  // Test user data
  const userInfo = {
    email: `test-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`,
    password: 'supersecret'
  }

  // Test player data
  const playerInfo = {
    firstname: 'Test',
    lastname: 'Player',
    sex: 'male'
  }

  // Test match data
  const matchInfo = {
    name: 'Test Match',
    isActive: true,
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
    endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 8 days from now
    description: 'Test match description',
    maxPlayersAmount: 10,
    equipmentCategories: JSON.stringify(['recurve', 'compound']),
    ageCategories: JSON.stringify(['senior', 'junior']),
    forMen: true,
    forWomen: true
  }

  let user: any
  let player: any
  let match: any
  let organizer: any
  let userParams: any

  // Setup before each test
  beforeEach(async () => {
    try {
      // Create a test user with unique email
      const uniqueEmail = `test-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`
      user = await usersService.create({
        ...userInfo,
        email: uniqueEmail
      })

      // Authenticate the user
      const { accessToken } = await authService.create({
        strategy: 'local',
        email: uniqueEmail,
        password: userInfo.password
      })

      // Create params with authentication
      userParams = {
        provider: 'rest',
        authentication: {
          strategy: 'jwt',
          accessToken
        },
        user
      }

      // Create a test player
      player = await playersService.create({
        ...playerInfo,
        userId: user.id
      }, userParams)

      // Create a test organizer
      organizer = await createTestOrganizer(user.id, userParams)

      // Create a test match
      match = await matchesService.create({
        ...matchInfo,
        organizerId: organizer.id
      }, userParams)
    } catch (error) {
      console.error('Setup error:', error)
      throw error
    }
  })

  // Cleanup after each test
  afterEach(async () => {
    try {
      // Clean up test data
      // Find all registrations for this player and remove them individually
      const registrations = await service.find({
        query: { playerId: player?.id }
      })

      for (const registration of registrations.data as any[]) { // Added 'as any[]' to handle potential type mismatch after refactor
        await service.remove(registration.id)
      }

      if (match?.id) await matchesService.remove(match.id)
      if (player?.id) await playersService.remove(player.id)
      if (organizer?.id) await app.service('organizers').remove(organizer.id)
      if (user?.id) await usersService.remove(user.id)
    } catch (error) {
      console.error('Cleanup error:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  describe('basic CRUD operations', () => {
    it('creates a registration', async () => {
      const registration = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      assert.ok(registration, 'Created a registration')
      assert.strictEqual(registration.matchId, match.id, 'Sets the match ID')
      assert.strictEqual(registration.playerId, player.id, 'Sets the player ID')
      assert.strictEqual(registration.status, 'pending', 'Sets status to pending')
      assert.strictEqual(registration.styleDivision, 'recurve', 'Sets style division')
      assert.strictEqual(registration.ageDivision, 'senior', 'Sets age division')
      assert.strictEqual(registration.genderDivision, 'male', 'Sets gender division')
    })

    it('finds registrations', async () => {
      // Create a registration first
      await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Find all registrations for the match
      const matchRegistrations = await service.find({
        query: {
          matchId: match.id
        }
      })

      assert.ok(matchRegistrations.data.length > 0, 'Found registrations for the match')

      // Find all registrations for the player
      const playerRegistrations = await service.find({
        query: {
          playerId: player.id
        }
      })

      assert.ok(playerRegistrations.data.length > 0, 'Found registrations for the player')
    })

    it('gets a registration by id', async () => {
      // Create a registration first
      const created = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Get the registration by ID
      const fetched = await service.get(created.id)

      assert.ok(fetched, 'Got registration by ID')
      assert.strictEqual(fetched.id, created.id, 'ID matches')
      assert.strictEqual(fetched.matchId, match.id, 'Match ID matches')
      assert.strictEqual(fetched.playerId, player.id, 'Player ID matches')
    })

    it('updates a registration', async () => {
      // Create a registration first
      const created = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Update the registration
      const updated = await service.patch(created.id, {
        status: 'confirmed',
        styleDivision: 'compound',
        genderDivision: 'mixed'
      })

      assert.ok(updated, 'Updated the registration')
      assert.strictEqual(updated.id, created.id, 'ID remains the same')
      assert.strictEqual(updated.status, 'confirmed', 'Status was updated')
      assert.strictEqual(updated.styleDivision, 'compound', 'Style division was updated')
      assert.strictEqual(updated.genderDivision, 'mixed', 'Gender division was updated')
    })

    it('removes a registration', async () => {
      // Create a registration first
      const created = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Remove the registration
      const removed = await service.remove(created.id)

      assert.ok(removed, 'Removed the registration')
      assert.strictEqual(removed.id, created.id, 'Removed the correct registration')

      // Try to get the removed registration
      try {
        await service.get(created.id)
        assert.fail('Should not be able to get removed registration')
      } catch (error: any) {
        assert.strictEqual(error.name, 'NotFound', 'Registration was actually removed')
      }
    })
  })

  describe('validation rules', () => {
    it('prevents duplicate registrations', async () => {
      // Create a registration first
      await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Try to create a duplicate registration
      try {
        await service.create({
          matchId: match.id,
          playerId: player.id,
          styleDivision: 'compound', // Different style, but same player and match
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow duplicate registration')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for duplicate registration')
        assert.ok(error.message.includes('already registered'), 'Error message mentions already registered')
      }
    })

    it('validates style division', async () => {
      // Try to register with an invalid style division
      try {
        await service.create({
          matchId: match.id,
          playerId: player.id,
          styleDivision: 'invalid-style',
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow invalid style division')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for invalid style division')
        assert.ok(error.message.includes('Invalid style division'), 'Error message mentions invalid style division')
      }
    })

    it('validates age division', async () => {
      // Try to register with an invalid age division
      try {
        await service.create({
          matchId: match.id,
          playerId: player.id,
          styleDivision: 'recurve',
          ageDivision: 'invalid-age',
          genderDivision: 'male'
        })
        assert.fail('Should not allow invalid age division')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for invalid age division')
        assert.ok(error.message.includes('Invalid age division'), 'Error message mentions invalid age division')
      }
    })

    it('validates gender division', async () => {
      // Try to register with an invalid gender division
      try {
        await service.create({
          matchId: match.id,
          playerId: player.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'invalid-gender'
        })
        assert.fail('Should not allow invalid gender division')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for invalid gender division')
        assert.ok(error.message.includes('Invalid gender division'), 'Error message mentions invalid gender division')
      }
    })

    it('validates match exists', async () => {
      // Try to register for a non-existent match
      try {
        await service.create({
          matchId: 9999, // Non-existent match ID
          playerId: player.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow registration for non-existent match')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for non-existent match')
        assert.ok(error.message.includes('Match not found'), 'Error message mentions match not found')
      }
    })

    it('validates player exists', async () => {
      // Try to register a non-existent player
      try {
        await service.create({
          matchId: match.id,
          playerId: 9999, // Non-existent player ID
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow registration for non-existent player')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for non-existent player')
      }
    })

    it('validates match is active', async () => {
      // Create an inactive match
      const inactiveMatch = await matchesService.create({
        ...matchInfo,
        name: 'Inactive Match',
        isActive: false,
        organizerId: organizer.id
      })

      // Try to register for an inactive match
      try {
        await service.create({
          matchId: inactiveMatch.id,
          playerId: player.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow registration for inactive match')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for inactive match')
        assert.ok(error.message.includes('not active'), 'Error message mentions match not active')
      }

      // Clean up
      await matchesService.remove(inactiveMatch.id)
    })

    it('validates registration is still open', async () => {
      // Create a match first
      const newMatch = await matchesService.create({
        ...matchInfo,
        name: 'Closed Registration Match',
        organizerId: organizer.id
      })

      // Then patch it to add registrationEnds in the past
      const closedMatch = await matchesService.patch(newMatch.id, {
        registrationEnds: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 1 day ago
      })

      // Try to register for a match with closed registration
      try {
        await service.create({
          matchId: closedMatch.id,
          playerId: player.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'male'
        })
        assert.fail('Should not allow registration for match with closed registration')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for closed registration')
        assert.ok(error.message.includes('Registration for this match has ended'), 'Error message mentions registration ended')
      }

      // Clean up
      await matchesService.remove(closedMatch.id)
    })

    it('validates match has not reached maximum players', async function () {
      // Increase timeout for this test
      this.timeout(10000);

      // Create a match with max 1 player
      const smallMatch = await matchesService.create({
        ...matchInfo,
        name: 'Small Match',
        maxPlayersAmount: 1,
        organizerId: organizer.id
      }, userParams)

      // Create another player with unique email
      const anotherUser = await usersService.create({
        email: `another-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`,
        password: 'supersecret'
      })

      // Authenticate the other user
      const { accessToken: anotherAccessToken } = await app.service('authentication').create({
        strategy: 'local',
        email: anotherUser.email,
        password: 'supersecret'
      })

      // Create params for the other user
      const anotherUserParams = {
        provider: 'rest',
        authentication: {
          strategy: 'jwt',
          accessToken: anotherAccessToken
        },
        user: anotherUser
      }

      const anotherPlayer = await playersService.create({
        ...playerInfo,
        firstname: 'Another',
        userId: anotherUser.id
      }, anotherUserParams)

      // Register the first player
      const registration = await service.create({
        matchId: smallMatch.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      }, userParams)

      // Try to register another player when match is full
      try {
        await service.create({
          matchId: smallMatch.id,
          playerId: anotherPlayer.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'male'
        }, anotherUserParams)
        assert.fail('Should not allow registration when match is full')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest when match is full')
        assert.ok(error.message.includes('maximum number of players'), 'Error message mentions maximum players')
      }

      // Clean up - need to remove registrations first due to foreign key constraints
      try {
        await service.remove(registration.id, userParams)
      } catch (error) {
        console.error('Error removing registration:', error)
      }

      try {
        await playersService.remove(anotherPlayer.id, anotherUserParams)
      } catch (error) {
        console.error('Error removing player:', error)
      }

      try {
        await usersService.remove(anotherUser.id)
      } catch (error) {
        console.error('Error removing user:', error)
      }

      try {
        await matchesService.remove(smallMatch.id, userParams)
      } catch (error) {
        console.error('Error removing match:', error)
      }
    })

    it('validates gender requirements', async () => {
      // Create a match for men only
      const menOnlyMatch = await matchesService.create({
        ...matchInfo,
        name: 'Men Only Match',
        forMen: true,
        forWomen: false,
        organizerId: organizer.id
      })

      // Create a female player with unique email
      const femaleUser = await usersService.create({
        email: `female-${Date.now()}-${Math.random().toString(36).substring(2, 15)}@example.com`,
        password: 'supersecret'
      })

      // Authenticate the female user
      const { accessToken: femaleAccessToken } = await authService.create({
        strategy: 'local',
        email: femaleUser.email,
        password: 'supersecret'
      })

      // Create params for the female user
      const femaleUserParams = {
        provider: 'rest',
        authentication: {
          strategy: 'jwt',
          accessToken: femaleAccessToken
        },
        user: femaleUser
      }

      const femalePlayer = await playersService.create({
        ...playerInfo,
        firstname: 'Female',
        sex: 'female',
        userId: femaleUser.id
      }, femaleUserParams)

      // Try to register a female player for a men-only match
      try {
        await service.create({
          matchId: menOnlyMatch.id,
          playerId: femalePlayer.id,
          styleDivision: 'recurve',
          ageDivision: 'senior',
          genderDivision: 'female'
        })
        assert.fail('Should not allow female player to register for men-only match')
      } catch (error: any) {
        assert.ok(error instanceof BadRequest, 'Throws BadRequest for gender mismatch')
        assert.ok(error.message.includes('gender requirements'), 'Error message mentions gender requirements')
      }

      // Clean up
      await playersService.remove(femalePlayer.id)
      await usersService.remove(femaleUser.id)
      await matchesService.remove(menOnlyMatch.id)
    })
  })

  describe('populate functionality', () => {
    it('populates player data when $populate=player is used', async () => {
      // Create a registration first
      const registration = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Find registrations with player populated
      const registrations = await service.find({
        query: {
          id: registration.id,
          $populate: ['player']
        }
      })

      assert.ok(registrations.data.length > 0, 'Found registrations')
      const populatedRegistration = registrations.data[0]

      assert.ok(populatedRegistration.player, 'Player data is populated')
      assert.strictEqual(populatedRegistration.player.id, player.id, 'Player ID matches')
      assert.strictEqual(populatedRegistration.player.firstname, player.firstname, 'Player firstname matches')
      assert.strictEqual(populatedRegistration.player.lastname, player.lastname, 'Player lastname matches')
      assert.strictEqual(populatedRegistration.player.sex, player.sex, 'Player sex matches')
    })

    it('works with get method and $populate=player', async () => {
      // Create a registration first
      const registration = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Get registration with player populated
      const populatedRegistration = await service.get(registration.id, {
        query: {
          $populate: ['player']
        }
      })

      assert.ok(populatedRegistration.player, 'Player data is populated')
      assert.strictEqual(populatedRegistration.player.id, player.id, 'Player ID matches')
      assert.strictEqual(populatedRegistration.player.firstname, player.firstname, 'Player firstname matches')
      assert.strictEqual(populatedRegistration.player.lastname, player.lastname, 'Player lastname matches')
    })

    it('returns only selected player fields when populated', async () => {
      // Create a registration first
      const registration = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Get registration with player populated
      const populatedRegistration = await service.get(registration.id, {
        query: {
          $populate: ['player']
        }
      })

      assert.ok(populatedRegistration.player, 'Player data is populated')

      // Check that only selected fields are included
      const expectedFields = ['id', 'firstname', 'lastname', 'sex', 'city', 'country']
      const playerKeys = Object.keys(populatedRegistration.player)

      for (const field of expectedFields) {
        assert.ok(playerKeys.includes(field), `Player should include ${field} field`)
      }

      // Check that some fields are NOT included (like userId, which is not in select)
      assert.ok(!playerKeys.includes('userId'), 'Player should not include userId field')
    })

    it('works without $populate parameter', async () => {
      // Create a registration first
      const registration = await service.create({
        matchId: match.id,
        playerId: player.id,
        styleDivision: 'recurve',
        ageDivision: 'senior',
        genderDivision: 'male'
      })

      // Get registration without populate
      const normalRegistration = await service.get(registration.id)

      assert.ok(normalRegistration, 'Registration is returned')
      assert.strictEqual(normalRegistration.playerId, player.id, 'Player ID is present')
      assert.ok(!normalRegistration.player, 'Player data is not populated')
    })
  })
})
