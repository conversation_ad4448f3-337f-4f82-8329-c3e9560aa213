{"name": "MatchRegistration", "schema": {"required": ["id", "matchId", "playerId", "registrationDate", "status", "createdAt", "updatedAt"], "type": "object", "properties": {"id": {"type": "number"}, "matchId": {"type": "number"}, "playerId": {"type": "number"}, "registrationDate": {"type": "string", "format": "date-time"}, "status": {"type": "string"}, "styleDivision": {"type": "string"}, "ageDivision": {"type": "string"}, "genderDivision": {"type": "string"}, "registrationDetails": {}, "equipmentId": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "match": {"$ref": "#/components/schemas/Match"}, "player": {"$ref": "#/components/schemas/Player"}, "isPaid": {"type": "boolean"}, "isConfirmed": {"type": "boolean"}}, "additionalProperties": false}}