<script setup lang="ts">
import { useRoute } from 'vue-router'
import TeamSwitcher from '../TeamSwitcher.vue'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import {
  SidebarTrigger,
} from '@/components/ui/sidebar'
import { Input } from '@/components/ui/input'
import { Bell, Settings, Trophy, Medal, Clock, Target, Map, Route, Crosshair } from 'lucide-vue-next'
import type { Component } from 'vue'
import type { PropType } from 'vue'
import { ref, computed } from 'vue'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

// Language selector state - computed to be reactive to locale changes
const languages = computed(() => [
  { code: 'pl', label: t('languages.polski'), flag: '/flags/pl.svg' },
  { code: 'en', label: t('languages.english'), flag: '/flags/gb.svg' },
  { code: 'cz', label: t('languages.cestina'), flag: '/flags/cz.svg' },
  { code: 'sk', label: t('languages.slovensky'), flag: '/flags/sk.svg' },
  { code: 'hu', label: t('languages.magyar'), flag: '/flags/hu.svg' },
  { code: 'de', label: t('languages.deutsch'), flag: '/flags/de.svg' },
  { code: 'fr', label: t('languages.francais'), flag: '/flags/fr.svg' }
])

// Find current language or default to English
const selectedLanguage = computed(() =>
  languages.value.find(lang => lang.code === locale.value) || languages.value[1]
)

function selectLanguage(lang: { code: string; label: string; flag: string }) {
  locale.value = lang.code
  // Store preference in localStorage
  localStorage.setItem('preferred-language', lang.code)
}


const route = useRoute()

// Medal counts (these would normally come from a store)
const goldMedals = ref(12)
const silverMedals = ref(12)
const bronzeMedals = ref(12)

// Stats (these would normally come from a store)
const matches = ref(12)
const totalHours = ref('15,32')
const accuracy = ref('63,74%')
const distance = ref('2 450 km')

</script>

<template>
  <header class="sticky top-0 z-50 flex h-14 shrink-0 items-center border-b bg-background px-3">
    <div class="flex flex-1 items-center justify-between">
      <div class="flex items-center gap-2">
        <SidebarTrigger />

        <!-- Stats Group 1: Medals -->
        <div class="flex items-center gap-1 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-yellow-500" />
            <span class="text-xs font-medium">{{ goldMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-gray-400" />
            <span class="text-xs font-medium">{{ silverMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-amber-600" />
            <span class="text-xs font-medium">{{ bronzeMedals }}</span>
          </div>
        </div>

        <!-- Stats Group 2: Medals by Category -->
        <div class="flex items-center gap-1 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-yellow-500" />
            <span class="text-xs font-medium">{{ goldMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-gray-400" />
            <span class="text-xs font-medium">{{ silverMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-amber-600" />
            <span class="text-xs font-medium">{{ bronzeMedals }}</span>
          </div>
        </div>

        <!-- Stats Group 3: Competition Stats -->
        <div class="flex items-center gap-2 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Target class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ matches }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ totalHours }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Crosshair class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ accuracy }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Route class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ distance }}</span>
          </div>
        </div>
      </div>

      <!-- Right side controls -->
      <div class="flex items-center space-x-2">
        <!-- Language Selector -->
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="icon" class="relative">
              <img :src="selectedLanguage.flag" :alt="selectedLanguage.code" class="h-4 w-4 rounded-full mr-1 object-cover" />

            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              v-for="lang in languages"
              :key="lang.code"
              @click="selectLanguage(lang)"
              :class="{'font-bold': lang.code === selectedLanguage.code}"
            >
              <img :src="lang.flag" :alt="lang.code" class="h-5 w-5 rounded-full mr-2 object-cover inline-block" />
              {{ lang.label }}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button variant="ghost" size="icon">
          <Bell class="h-5 w-5" />
        </Button>

        <Button variant="ghost" size="icon">
          <Settings class="h-5 w-5" />
        </Button>


      </div>
    </div>
  </header>
</template>
