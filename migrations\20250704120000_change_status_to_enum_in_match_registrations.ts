import { Knex } from 'knex'

// Migration: Change status to enum for match_registrations
export async function up(knex: Knex): Promise<void> {
    await knex.raw(`
    DO $$
    BEGIN
      IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'match_registration_status') THEN
        CREATE TYPE match_registration_status AS ENUM (
          'imported', 'draft', 'submitted', 'pending', 'reviewing', 'info', 'ineligible', 'approved', 'payment', 'paid', 'failed', 'withdrawn', 'assigned', 'ready', 'active', 'completed', 'noshow', 'disqualified', 'rejected', 'suspended', 'expired'
        );
      END IF;
    END$$;
  `)
    await knex.schema.alterTable('match_registrations', (table) => {
        table.specificType('status', 'match_registration_status').notNullable().defaultTo('pending').alter()
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('match_registrations', (table) => {
        table.string('status').notNullable().defaultTo('pending').alter()
    })
    await knex.raw(`
    DROP TYPE IF EXISTS match_registration_status;
  `)
}
