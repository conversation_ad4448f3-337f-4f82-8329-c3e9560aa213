{"name": "MatchRegistrationQuery", "schema": {"type": "object", "properties": {"$sort": {"type": "object", "properties": {"id": {"maximum": 1, "minimum": -1, "type": "integer"}, "matchId": {"maximum": 1, "minimum": -1, "type": "integer"}, "playerId": {"maximum": 1, "minimum": -1, "type": "integer"}, "registrationDate": {"maximum": 1, "minimum": -1, "type": "integer"}, "status": {"maximum": 1, "minimum": -1, "type": "integer"}, "styleDivision": {"maximum": 1, "minimum": -1, "type": "integer"}, "ageDivision": {"maximum": 1, "minimum": -1, "type": "integer"}, "genderDivision": {"maximum": 1, "minimum": -1, "type": "integer"}, "equipmentId": {"maximum": 1, "minimum": -1, "type": "integer"}}, "additionalProperties": false}, "$select": {"maxItems": 9, "type": "array", "items": {"enum": ["id", "matchId", "playerId", "registrationDate", "status", "styleDivision", "ageDivision", "genderDivision", "equipmentId"], "type": "string"}}, "$and": {"type": "array", "items": {"anyOf": [{"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "matchId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "playerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "registrationDate": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "status": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "styleDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "ageDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "genderDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "equipmentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}, {"type": "object", "properties": {"$or": {"type": "array", "items": {"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "matchId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "playerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "registrationDate": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "status": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "styleDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "ageDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "genderDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "equipmentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}}}, "required": ["$or"]}]}}, "$or": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "matchId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "playerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "registrationDate": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "status": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "styleDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "ageDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "genderDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "equipmentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}, "additionalProperties": false}}, "id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "matchId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "playerId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "registrationDate": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "status": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "styleDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "ageDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "genderDivision": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "equipmentId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}, "additionalProperties": false}}