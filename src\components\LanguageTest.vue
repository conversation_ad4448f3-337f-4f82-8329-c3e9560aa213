<template>
  <div class="p-4 border rounded-lg bg-card">
    <h3 class="text-lg font-semibold mb-4">{{ t('app.name') }} - i18n Test</h3>
    
    <div class="space-y-2 mb-4">
      <p><strong>Current locale:</strong> {{ locale }}</p>
      <p><strong>App name:</strong> {{ t('app.name') }}</p>
      <p><strong>Sign In:</strong> {{ t('auth.signIn') }}</p>
      <p><strong>Email:</strong> {{ t('auth.email') }}</p>
      <p><strong>Matches:</strong> {{ t('navigation.matches') }}</p>
      <p><strong>Search:</strong> {{ t('navigation.search') }}</p>
    </div>

    <div class="flex gap-2">
      <button 
        @click="setLocale('en')"
        class="px-3 py-1 bg-blue-500 text-white rounded"
        :class="{ 'bg-blue-700': locale === 'en' }"
      >
        English
      </button>
      <button 
        @click="setLocale('pl')"
        class="px-3 py-1 bg-blue-500 text-white rounded"
        :class="{ 'bg-blue-700': locale === 'pl' }"
      >
        Polski
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

const setLocale = (newLocale: string) => {
  locale.value = newLocale
  localStorage.setItem('preferred-language', newLocale)
}
</script>
